import React, { useState, useCallback } from 'react'
import { useAuthStatus } from '../hooks/useAuth'
import { CodeReviewerHeader } from '../components/CodeReviewerHeader'
import { AuthenticationPrompt } from '../components/AuthenticationPrompt'
import { WorkflowSteps } from '../components/WorkflowSteps'
import { AssignedWorkPanel } from '../components/AssignedWorkPanel'
import { ConfigureReviewStep } from '../components/ConfigureReviewStep'
import { ReviewProgressStep } from '../components/ReviewProgressStep'
import { ReviewResultsStep } from '../components/ReviewResultsStep'
import type { ServiceHealth } from '../components/ServiceHealthIndicator'
import { 
  useCodeReviewerStore,
  useCurrentSelection,
  useReviewConfig,
  useReviewSession
} from '../store/useCodeReviewerStore'
import type { ReviewSession } from '../types/enhanced-review'
import type { MultiAgentReviewStatus } from '../components/MultiAgentProgressTracker'
import { codeReviewerService } from '../services/codeReviewer/CodeReviewerService'
import { MultiAgentReviewService } from '../services/multiAgent/MultiAgentReviewService'
import { useWebSocketConnection } from '../hooks/useWebSocketConnection'
import { useMultiAgentReview } from '../hooks/useMultiAgentReview'
import { useProgressPolling } from '../hooks/useProgressPolling'
import { useWorkflowAutoAdvance } from '../hooks/useWorkflowAutoAdvance'
import { WorktreeStatusIndicator } from '../components/worktree/WorktreeStatusIndicator'
import { useWorktreeStatus } from '../contexts/WorktreeStatusContext'
import { featureFlags } from '../config/featureFlags'
import type { MultiAgentReviewRequest, AgentType, ReviewMode as MultiAgentReviewMode } from '../types/multi-agent'

// Types
interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
  author: string
  created_date: string
  updated_date: string
  comments: number
  state: string
  reviewers: string[]
  jira_ticket?: string
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  acceptance_criteria?: string[]
  related_prs: number[]
}

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: Record<string, unknown>
}

type ReviewStep = 'select-work' | 'configure-review' | 'review-progress' | 'review-results'

export const CodeReviewer: React.FC = () => {
  const { isAuthenticated } = useAuthStatus()
  const { status: worktreeStatus } = useWorktreeStatus()
  
  // Local state
  const [currentStep, setCurrentStep] = useState<ReviewStep>('select-work')
  const [isStartingReview, setIsStartingReview] = useState(false)
  const [progressEvents, setProgressEvents] = useState<ProgressEvent[]>([])
  const [currentProgressStep, setCurrentProgressStep] = useState<string>('')
  const [serviceHealth, setServiceHealth] = useState<Record<string, ServiceHealth>>({})
  const [_multiAgentStatus, setMultiAgentStatus] = useState<MultiAgentReviewStatus | null>(null)
  const [_multiAgentReviewId, setMultiAgentReviewId] = useState<string | null>(null)

  // Mark variables as used to avoid TypeScript warnings (they are used by handlers below)
  void serviceHealth
  void _multiAgentStatus
  void _multiAgentReviewId
  
  // Store hooks
  const { selectPR, selectTicket } = useCodeReviewerStore()
  const { selectedPR, selectedTicket } = useCurrentSelection()
  const { mode, repositoryPath, setMode } = useReviewConfig()
  const { activeSession, progress, addSession, updateSession } = useReviewSession()
  
  // Multi-Agent Service Integration
  const [_multiAgentService] = useState(() => new MultiAgentReviewService())
  void _multiAgentService // Mark as used to avoid TypeScript warning
  const isMultiAgentEnabled = featureFlags.isMultiAgentEnabled()
  // Auto-enable Multi-Agent mode if feature is enabled, unless explicitly set to legacy modes
  const isMultiAgentMode = isMultiAgentEnabled && 
    (mode as string === 'multi_agent' || mode as string === 'full' || 
     (mode as string !== 'quick' && mode as string !== 'bug_analysis' && mode as string !== 'ac_only'))
  
  // Multi-Agent Review Hook
  const multiAgentReview = useMultiAgentReview({
    enable_websocket: isMultiAgentMode && isMultiAgentEnabled,
    enable_debug_logs: import.meta.env.NODE_ENV === 'development',
    polling_interval: 5000,
    auto_fetch_results: true,
    on_review_started: (response) => {
      console.log('🚀 Multi-Agent Review Started:', response)
      setMultiAgentReviewId(response.review_id)
      
      // Create corresponding ReviewSession for compatibility
      const session: ReviewSession = {
        session_id: response.session_id,
        branch_name: selectedPR?.branch || '',
        pr_url: selectedPR ? `https://bitbucket.org/${selectedPR.workspace}/${selectedPR.repository.split('/').pop()}/pull-requests/${selectedPR.id}` : '',
        status: response.status as 'initializing' | 'running' | 'completed' | 'error',
        progress: 5,
        progress_message: 'Multi-agent orchestration started',
        created_at: response.created_at,
        worktree_path: repositoryPath
      }
      
      addSession(session)
      setCurrentStep('review-progress')
    },
    on_status_update: (status) => {
      console.log('📊 Multi-Agent Status Update:', status)
      
      // Convert to MultiAgentReviewStatus format
      const _multiAgentStatus: MultiAgentReviewStatus = {
        review_id: status.review_id,
        status: status.status,
        progress: status.progress,
        agent_statuses: status.agent_statuses,
        started_at: status.started_at,
        completed_at: status.completed_at,
        estimated_remaining_time: status.estimated_remaining_time,
        active_agents: status.active_agents,
        completed_agents: status.completed_agents,
        failed_agents: status.failed_agents,
        context_status: status.context_status
      }
      
      setMultiAgentStatus(_multiAgentStatus)
      
      // Update corresponding ReviewSession
      if (activeSession && activeSession.session_id === status.session_id) {
        updateSession(status.session_id, {
          status: status.status as ReviewSession['status'],
          progress: status.progress,
          progress_message: `Multi-agent review: ${status.active_agents.length} agents running`,
          completed_at: status.completed_at
        })
      }
    },
    on_review_completed: (results) => {
      console.log('✅ Multi-Agent Review Completed:', results)
      
      if (activeSession) {
        updateSession(activeSession.session_id, {
          status: 'completed',
          progress: 100,
          progress_message: 'Multi-agent review completed!',
          completed_at: results.completed_at,
          results: results as any
        })
      }
      
      // Auto-advance to results step
      setCurrentStep('review-results')
    },
    on_error: (error) => {
      console.error('❌ Multi-Agent Error:', error)
      
      if (activeSession) {
        updateSession(activeSession.session_id, {
          status: 'error',
          progress_message: `Multi-agent review failed: ${error.error_message}`,
          error: error.error_message
        })
      }
      
      // Show error in progress events
      setProgressEvents(prev => [...prev, {
        id: `error-${Date.now()}`,
        type: 'error',
        message: `Multi-agent review error: ${error.error_message}`,
        timestamp: new Date(),
        data: { error }
      }])
    }
  })

  // Event handlers for hooks
  const handleProgressEvent = useCallback((event: ProgressEvent) => {
    setProgressEvents(prev => {
      console.log('📝 Current progress events:', prev.length)
      const newEvents = [...prev, event]
      console.log('📝 New progress events:', newEvents.length)
      return newEvents
    })
  }, [])

  const handleProgressStepUpdate = useCallback((step: string) => {
    setCurrentProgressStep(step)
  }, [])

  const handleSessionUpdate = useCallback((sessionId: string, updates: Partial<ReviewSession>) => {
    updateSession(sessionId, updates)
  }, [updateSession])

  // Custom hooks
  useWebSocketConnection({
    activeSession,
    onProgressEvent: handleProgressEvent,
    onProgressStepUpdate: handleProgressStepUpdate,
    onSessionUpdate: handleSessionUpdate,
    reviewMode: 'multi_agent' // Force multi-agent WebSocket only - legacy service doesn't support WebSocket
  })

  useProgressPolling({
    activeSession,
    onSessionUpdate: handleSessionUpdate
  })

  useWorkflowAutoAdvance({
    selectedPR,
    selectedTicket,
    activeSession,
    currentStep,
    onStepChange: setCurrentStep
  })

  // Event handlers
  const handlePRSelect = (pr: AssignedPR) => {
    selectPR(pr)
  }

  const handleTicketSelect = (ticket: AssignedTicket | null) => {
    selectTicket(ticket)
  }

  // Handler for Step 1 → Step 2 transition (with AC generation if needed)
  const handleContinueToConfigure = async (pr: AssignedPR, ticket?: AssignedTicket) => {
    console.log('🚀 Moving to configure step with:', { pr: pr.branch, ticket: ticket?.ticket_id })
    
    // Update selected items first
    selectPR(pr)
    selectTicket(ticket || null)
    
    // Check if ticket needs AC generation (has 0 AC)
    if (ticket && (ticket.acceptance_criteria_count === 0 || !ticket.acceptance_criteria || ticket.acceptance_criteria.length === 0)) {
      console.log('🤖 Ticket has 0 AC, generating with Claude AI...')
      
      try {
        // Show loading state
        setProgressEvents(prev => [...prev, {
          id: `ac-generation-${Date.now()}`,
          type: 'ac_generation_started',
          message: `Generiere Acceptance Criteria für ${ticket.ticket_id}...`,
          timestamp: new Date()
        }])

        const result = await codeReviewerService.generateAcceptanceCriteria({
          ticket_id: ticket.ticket_id,
          summary: ticket.summary,
          description: ticket.description
        })

        if (result.success && result.acceptance_criteria) {
          console.log('✅ AC generated successfully:', result.acceptance_criteria.length, 'criteria')
          
          // Update the ticket with generated AC
          const updatedTicket: AssignedTicket = {
            ...ticket,
            acceptance_criteria: result.acceptance_criteria,
            acceptance_criteria_count: result.acceptance_criteria?.length || 0
          }
          
          // Update the store with the enhanced ticket
          selectTicket(updatedTicket)
          
          setProgressEvents(prev => [...prev, {
            id: `ac-generation-success-${Date.now()}`,
            type: 'ac_generation_completed',
            message: `✅ ${result.acceptance_criteria?.length || 0} Acceptance Criteria generiert`,
            timestamp: new Date()
          }])
        } else {
          console.error('❌ AC generation failed:', result.error)
          setProgressEvents(prev => [...prev, {
            id: `ac-generation-error-${Date.now()}`,
            type: 'ac_generation_error',
            message: `⚠️ AC Generation fehlgeschlagen: ${result.error || 'Unbekannter Fehler'}`,
            timestamp: new Date()
          }])
        }
      } catch (error) {
        console.error('❌ AC generation error:', error)
        setProgressEvents(prev => [...prev, {
          id: `ac-generation-error-${Date.now()}`,
          type: 'ac_generation_error',
          message: `⚠️ AC Generation Fehler: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`,
          timestamp: new Date()
        }])
      }
    } else {
      console.log('✅ Ticket already has AC, proceeding directly')
    }
    
    // Move to configure step
    setCurrentStep('configure-review')
  }

  // Enhanced Review Start Handler with Multi-Agent Support
  const handleStartReview = async () => {
    if (!selectedPR) return

    setIsStartingReview(true)
    
    // Clear previous progress events and states
    setProgressEvents([])
    setCurrentProgressStep('')
    setMultiAgentStatus(null)
    setMultiAgentReviewId(null)
    
    try {
      console.log('🚀 Starting review with mode:', mode, {
        pr: selectedPR,
        ticket: selectedTicket,
        repositoryPath,
        isMultiAgentMode,
        isMultiAgentEnabled
      })
      
      // Service Health Check for Multi-Agent
      if (isMultiAgentMode && isMultiAgentEnabled) {
        console.log('🔍 Multi-Agent mode enabled - using Multi-Agent service')
        
        // TODO: Implement proper service health check
        // For now, assume Multi-Agent service is available since it's enabled
        console.log('✅ Multi-Agent service assumed healthy')
      }
      
      const ticketData = selectedTicket ? {
        ticket_id: selectedTicket.ticket_id,
        summary: selectedTicket.summary,
        description: selectedTicket.description,
        acceptance_criteria: selectedTicket.acceptance_criteria || [],
        acceptance_criteria_count: selectedTicket.acceptance_criteria_count
      } : null
      
      // Multi-Agent Review Path
      if (isMultiAgentMode && isMultiAgentEnabled) {
        console.log('🤖 Starting Multi-Agent Review...')
        
        const multiAgentRequest: MultiAgentReviewRequest = {
          branch_name: selectedPR.branch,
          repository_path: repositoryPath,
          pr_url: `https://bitbucket.org/${selectedPR.workspace}/${selectedPR.repository.split('/').pop()}/pull-requests/${selectedPR.id}`,
          review_mode: (mode as string === 'quick' ? 'quick' : 
                       mode as string === 'bug_analysis' ? 'bug_analysis' :
                       mode as string === 'ac_only' ? 'ac_only' : 'full') as MultiAgentReviewMode,
          agent_config: {
            enabled_agents: [
              'acceptance_criteria',
              'bug_detection', 
              'security_analysis',
              'logic_analysis',
              'quality_analysis',
              'architecture_analysis',
              'summary'
            ] as AgentType[],
            timeout_seconds: 300,
            concurrent_agents: 7
          },
          jira_ticket: ticketData ? {
            ticket_id: ticketData.ticket_id,
            summary: ticketData.summary,
            description: ticketData.description,
            acceptance_criteria: ticketData.acceptance_criteria
          } : undefined,
          context_config: {
            include_git_history: true,
            include_related_files: true,
            max_context_size: 100000
          }
        }
        
        // Use the Multi-Agent Hook to start the review
        await multiAgentReview.startReview(multiAgentRequest)
        
      } else {
        // Legacy Review Path
        console.log('🔄 Starting Legacy Review...')
        
        const response = await codeReviewerService.startReview({
          branch_name: selectedPR.branch,
          repository_path: repositoryPath,
          pr_url: `https://bitbucket.org/${selectedPR.workspace}/${selectedPR.repository.split('/').pop()}/pull-requests/${selectedPR.id}`,
          review_mode: mode,
          jira_ticket: ticketData
        })

        console.log('🔥 Legacy API Response:', response)

        if (response.success && response.session) {
          // Create session object from response
          const session = {
            session_id: response.session.session_id,
            branch_name: response.session.branch_name,
            pr_url: `https://bitbucket.org/${selectedPR.workspace}/${selectedPR.repository.split('/').pop()}/pull-requests/${selectedPR.id}`,
            status: response.session.status as 'initializing' | 'running' | 'completed' | 'error',
            progress: response.session.progress,
            progress_message: response.session.progress_message,
            created_at: response.session.created_at,
            worktree_path: repositoryPath
          }
          
          console.log('🔥 Created legacy session object:', session)
          
          addSession(session)
          setCurrentStep('review-progress')
          
          console.log('🔥 Legacy review started successfully')
        } else {
          console.error('Failed to start legacy review:', response.error)
          throw new Error(response.error || 'Failed to start review')
        }
      }
      
    } catch (error) {
      console.error('Error starting review:', error)
      
      setProgressEvents(prev => [...prev, {
        id: `error-${Date.now()}`,
        type: 'review_start_error',
        message: `Failed to start review: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        data: { error }
      }])
      
    } finally {
      setIsStartingReview(false)
    }
  }

  const handleResetWorkflow = () => {
    selectPR(null)
    selectTicket(null)
    setCurrentStep('select-work')
    setMultiAgentStatus(null)
    setMultiAgentReviewId(null)
    setProgressEvents([])
    setCurrentProgressStep('')
    
    // Reset multi-agent hook state
    multiAgentReview.reset()
  }
  
  // Service Health Change Handler (currently unused but may be needed for future service monitoring)
  const handleServiceHealthChange = useCallback((services: Record<string, ServiceHealth>) => {
    setServiceHealth(services)
    console.log('🏥 Service Health Updated:', services)
  }, [])
  void handleServiceHealthChange // Mark as used to avoid TypeScript warning

  // Multi-Agent Status Update Handler (currently unused but may be needed for future status updates)
  const handleMultiAgentStatusUpdate = useCallback((status: MultiAgentReviewStatus) => {
    setMultiAgentStatus(status)
  }, [])
  void handleMultiAgentStatusUpdate // Mark as used to avoid TypeScript warning

  const renderStepContent = () => {
    switch (currentStep) {
      case 'select-work':
        return (
          <AssignedWorkPanel
            onPRSelect={handlePRSelect}
            onTicketSelect={handleTicketSelect}
            onStartReview={handleContinueToConfigure}
          />
        )
      
      case 'configure-review':
        return (
          <ConfigureReviewStep
            selectedPR={selectedPR}
            selectedTicket={selectedTicket}
            mode={mode}
            repositoryPath={repositoryPath}
            onModeSelect={setMode}
            onStartReview={handleStartReview}
            isStartingReview={isStartingReview}
          />
        )
      
      case 'review-progress':
        return (
          <ReviewProgressStep
            activeSession={activeSession}
            selectedPR={selectedPR}
            progress={progress}
            progressEvents={progressEvents}
            currentProgressStep={currentProgressStep}
            multiAgentStatus
            onMultiAgentStatusUpdate
            enableMultiAgentHook=false  
            multiAgentReviewId={_multiAgentReviewId}
            onMultiAgentReviewStarted
            onMultiAgentError
            mode={mode}
          />
        )
      
      case 'review-results':
        return (
          <ReviewResultsStep
            activeSession={activeSession}
            selectedPR={selectedPR}
            onSessionUpdate={handleSessionUpdate}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl animate-fade-in relative z-10">
      {/* Header */}
      <CodeReviewerHeader />

      {/* Authentication Check */}
      {!isAuthenticated ? (
        <AuthenticationPrompt />
      ) : (
        <div className="space-y-6">
          {/* Worktree Status Check */}
          {(!worktreeStatus.isConfigured || !worktreeStatus.isValid) && (
            <WorktreeStatusIndicator 
              showFullCard={true}
              className="border-amber-200 bg-amber-50/50"
            />
          )}

          {/* Workflow Steps */}
          <WorkflowSteps 
            currentStep={currentStep}
            onResetWorkflow={handleResetWorkflow}
          />

          {/* Step Content - Block if worktree not configured */}
          {worktreeStatus.isConfigured && worktreeStatus.isValid ? (
            renderStepContent()
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>Please configure your Git Worktree settings to proceed with code reviews.</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}